#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix the final remaining issues
 */

const fs = require('fs');
const path = require('path');

const services = [
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

console.log('🔧 Fixing final remaining issues...');

function fixFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix shared logger imports in routes and other files
    const patterns = [
      {
        old: /require\(['"`]\.\.\/\.\.\/\.\.\/\.\.\/shared\/utils\/logger['"`]\)/g,
        new: "require('../utils/logger')"
      },
      {
        old: /require\(['"`]\.\.\/\.\.\/\.\.\/shared\/utils\/logger['"`]\)/g,
        new: "require('../utils/logger')"
      },
      {
        old: /require\(['"`]\.\.\/\.\.\/shared\/utils\/logger['"`]\)/g,
        new: "require('../utils/logger')"
      },
      {
        old: /require\(['"`]\.\.\/shared\/utils\/logger['"`]\)/g,
        new: "require('../utils/logger')"
      },
      // Fix HealthCheck imports
      {
        old: /const \{ HealthCheck, commonChecks \} = require\(['"`]\.\.\/utils\/health-check['"`]\);/g,
        new: "const { HealthCheck, commonChecks } = require('./utils/health-check');"
      },
      {
        old: /const HealthCheck = require\(['"`]\.\.\/utils\/health-check['"`]\);/g,
        new: "const { HealthCheck } = require('./utils/health-check');"
      }
    ];

    for (const { old, new: replacement } of patterns) {
      if (content.match(old)) {
        content = content.replace(old, replacement);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}: ${error.message}`);
  }

  return false;
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return 0;
  }

  let fixedCount = 0;
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
      fixedCount += processDirectory(itemPath);
    } else if (item.endsWith('.js')) {
      if (fixFile(itemPath)) {
        console.log(`  ✅ Fixed ${path.relative(process.cwd(), itemPath)}`);
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    continue;
  }

  console.log(`\n🔧 Processing ${service}...`);
  
  const srcPath = path.join(servicePath, 'src');
  const fixedCount = processDirectory(srcPath);
  
  if (fixedCount > 0) {
    console.log(`  ✅ Fixed ${fixedCount} file(s)`);
  } else {
    console.log(`  ✅ Already clean`);
  }
}

console.log('\n✅ Final issues fixed!');
