#!/usr/bin/env node

/**
 * <PERSON>ript to set up simple event bus for services that need event sourcing
 * This replaces the complex shared event infrastructure with simple, isolated event handling
 */

const fs = require('fs');
const path = require('path');

const servicesWithEvents = ['auth-service', 'community-service', 'link-service'];

const eventBusTemplate = `/**
 * Simple Event Bus for {SERVICE_NAME}
 * Lightweight event handling without shared dependencies
 */

const EventEmitter = require('events');
const logger = require('./logger');

class SimpleEventBus extends EventEmitter {
  constructor(serviceName = '{SERVICE_NAME}') {
    super();
    this.serviceName = serviceName;
    this.events = [];
    this.isConnected = true; // Always connected in simple mode
  }

  // Publish an event
  async publish(eventType, data, options = {}) {
    try {
      const event = {
        type: eventType,
        data,
        timestamp: new Date().toISOString(),
        source: this.serviceName,
        id: \`\${Date.now()}-\${Math.random().toString(36).substr(2, 9)}\`
      };

      // Store event locally
      this.events.push(event);

      // Emit event locally
      this.emit('event', event);
      this.emit(eventType, event);

      logger.info('Event published', { type: eventType, source: this.serviceName });
      return event;
    } catch (error) {
      logger.error('Failed to publish event', { error: error.message, type: eventType });
      throw error;
    }
  }

  // Subscribe to events
  async subscribe(eventPattern, handler) {
    try {
      this.on(eventPattern, handler);
      logger.info('Subscribed to events', { pattern: eventPattern });
    } catch (error) {
      logger.error('Failed to subscribe to events', { error: error.message, pattern: eventPattern });
    }
  }

  // Service-specific event publishers
  async publishAuthEvent(action, data) {
    return this.publish(\`auth:\${action}\`, data);
  }

  async publishUserEvent(action, data) {
    return this.publish(\`user:\${action}\`, data);
  }

  async publishCommunityEvent(action, data) {
    return this.publish(\`community:\${action}\`, data);
  }

  async publishLinkEvent(action, data) {
    return this.publish(\`link:\${action}\`, data);
  }

  async publishSystemEvent(action, data) {
    return this.publish(\`system:\${action}\`, data);
  }

  // Service-specific event subscribers
  async subscribeToAuthEvents(handler) {
    return this.subscribe('auth:*', handler);
  }

  async subscribeToUserEvents(handler) {
    return this.subscribe('user:*', handler);
  }

  async subscribeToCommunityEvents(handler) {
    return this.subscribe('community:*', handler);
  }

  async subscribeToLinkEvents(handler) {
    return this.subscribe('link:*', handler);
  }

  async subscribeToSystemEvents(handler) {
    return this.subscribe('system:*', handler);
  }

  // Get stored events (for testing/monitoring)
  getEvents() {
    return this.events;
  }

  getMockEvents() {
    return this.events;
  }

  // Clear stored events
  clearEvents() {
    this.events = [];
  }

  // Connection status
  isEventBusConnected() {
    return this.isConnected;
  }
}

module.exports = SimpleEventBus;`;

console.log('🔧 Setting up simple event bus for services...');

for (const service of servicesWithEvents) {
  const servicePath = path.join('services', service);
  
  // Check if service exists
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️ Service ${service} not found, skipping...`);
    continue;
  }

  // Create utils directory if it doesn't exist
  const utilsPath = path.join(servicePath, 'src', 'utils');
  if (!fs.existsSync(utilsPath)) {
    fs.mkdirSync(utilsPath, { recursive: true });
  }

  // Create event bus file
  const eventBusPath = path.join(utilsPath, 'eventBus.js');
  const serviceContent = eventBusTemplate.replace(/{SERVICE_NAME}/g, service);
  
  fs.writeFileSync(eventBusPath, serviceContent);
  console.log(`✅ Created ${service}/src/utils/eventBus.js`);

  // Update event handler if it exists
  const eventHandlerPath = path.join(servicePath, 'src', 'events', `${service.replace('-service', '')}EventHandler.js`);
  if (fs.existsSync(eventHandlerPath)) {
    try {
      let content = fs.readFileSync(eventHandlerPath, 'utf8');
      
      // Replace imports
      content = content.replace(
        /const EventBus = require\('\.\.\/\.\.\/\.\.\/shared\/eventBus\/eventBus'\);/g,
        "const SimpleEventBus = require('../utils/eventBus');"
      );
      
      content = content.replace(
        /const \{ EventTypes, EventHelpers \} = require\('\.\.\/\.\.\/\.\.\/shared\/eventBus\/eventTypes'\);/g,
        "// Event types are now handled locally"
      );
      
      // Replace EventBus constructor
      content = content.replace(
        /this\.eventBus = new EventBus\(\{[\s\S]*?\}\);/g,
        `this.eventBus = new SimpleEventBus('${service}');`
      );
      
      fs.writeFileSync(eventHandlerPath, content);
      console.log(`✅ Updated ${service} event handler`);
    } catch (error) {
      console.error(`❌ Error updating ${service} event handler: ${error.message}`);
    }
  }
}

console.log('✅ Simple event bus setup complete!');
console.log('💡 Services now have isolated event handling without shared dependencies');
