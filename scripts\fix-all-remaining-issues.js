#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix all remaining issues after removing shared folders
 * This will ensure all services work properly when running npm start
 */

const fs = require('fs');
const path = require('path');

const services = [
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

console.log('🔧 Fixing all remaining issues after shared folder removal...');

// Fix import paths in all files
function fixImportsInFile(filePath, serviceName) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix logger imports
    const loggerPatterns = [
      {
        old: /require\(['"`]\.\.\/\.\.\/\.\.\/shared\/utils\/logger['"`]\)/g,
        new: "require('./utils/logger')"
      },
      {
        old: /require\(['"`]\.\.\/utils\/logger['"`]\)/g,
        new: "require('./utils/logger')"
      },
      {
        old: /require\(['"`]\.\.\/\.\.\/utils\/logger['"`]\)/g,
        new: "require('../utils/logger')"
      }
    ];

    // Fix env-loader imports
    const envPatterns = [
      {
        old: /const \{ setupEnvironment, getRequiredVarsForService \} = require\(['"`]\.\.\/utils\/env-loader['"`]\);/g,
        new: "const { setupEnvironment, getRequiredVarsForService } = require('./utils/env-loader');"
      },
      {
        old: /const \{ loadEnvironmentVariables \} = require\(['"`]\.\.\/utils\/env-loader['"`]\);/g,
        new: "const { setupEnvironment } = require('../utils/env-loader');"
      },
      {
        old: /loadEnvironmentVariables\(/g,
        new: "setupEnvironment("
      }
    ];

    // Fix shared utilities imports
    const utilPatterns = [
      {
        old: /const \{ Logger \} = require\(['"`]@factcheck\/shared['"`]\);/g,
        new: "const logger = require('./utils/logger');"
      },
      {
        old: /const logger = new Logger\([^)]*\);/g,
        new: "// Logger already initialized"
      },
      {
        old: /const \{ HealthCheck, commonChecks \} = require\(['"`]@factcheck\/shared['"`]\);/g,
        new: "const { HealthCheck, commonChecks } = require('./utils/health-check');"
      },
      {
        old: /const \{ ResponseFormatter \} = require\(['"`]@factcheck\/shared['"`]\);/g,
        new: "const ResponseFormatter = require('./utils/response');"
      }
    ];

    // Apply all patterns
    const allPatterns = [...loggerPatterns, ...envPatterns, ...utilPatterns];
    
    for (const { old, new: replacement } of allPatterns) {
      if (content.match(old)) {
        content = content.replace(old, replacement);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}: ${error.message}`);
  }

  return false;
}

// Recursively fix all JS files in a directory
function fixAllFilesInDirectory(dirPath, serviceName) {
  if (!fs.existsSync(dirPath)) {
    return 0;
  }

  let fixedCount = 0;
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
      fixedCount += fixAllFilesInDirectory(itemPath, serviceName);
    } else if (item.endsWith('.js')) {
      if (fixImportsInFile(itemPath, serviceName)) {
        console.log(`  ✅ Fixed ${path.relative(process.cwd(), itemPath)}`);
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// Ensure all required utilities exist
function ensureUtilitiesExist(servicePath, serviceName) {
  const utilsPath = path.join(servicePath, 'src', 'utils');
  
  if (!fs.existsSync(utilsPath)) {
    fs.mkdirSync(utilsPath, { recursive: true });
  }

  const requiredUtils = ['logger.js', 'health-check.js', 'response.js', 'env-loader.js'];
  
  for (const util of requiredUtils) {
    const utilPath = path.join(utilsPath, util);
    if (!fs.existsSync(utilPath)) {
      console.log(`  ⚠️ Missing ${util}, creating...`);
      // Copy from auth-service as template
      const templatePath = path.join('services', 'auth-service', 'src', 'utils', util);
      if (fs.existsSync(templatePath)) {
        let content = fs.readFileSync(templatePath, 'utf8');
        content = content.replace(/auth-service/g, serviceName);
        fs.writeFileSync(utilPath, content);
        console.log(`  ✅ Created ${util}`);
      }
    }
  }
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️ Service ${service} not found, skipping...`);
    continue;
  }

  console.log(`\n🔧 Processing ${service}...`);
  
  // Ensure all utilities exist
  ensureUtilitiesExist(servicePath, service);
  
  // Fix all imports in src directory
  const srcPath = path.join(servicePath, 'src');
  const fixedCount = fixAllFilesInDirectory(srcPath, service);
  
  if (fixedCount > 0) {
    console.log(`  ✅ Fixed ${fixedCount} file(s)`);
  } else {
    console.log(`  ✅ No issues found`);
  }
}

console.log('\n✅ All remaining issues have been fixed!');
console.log('💡 All services should now work properly with npm start');
