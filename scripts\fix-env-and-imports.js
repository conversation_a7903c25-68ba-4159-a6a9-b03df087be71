#!/usr/bin/env node

/**
 * Script to fix environment loading and remaining import paths
 */

const fs = require('fs');
const path = require('path');

const services = [
  'api-gateway',
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

console.log('🔧 Fixing environment loading and import paths...');

// Fix env-loader to look for root .env file
function fixEnvLoader(servicePath) {
  const envLoaderPath = path.join(servicePath, 'src', 'utils', 'env-loader.js');
  
  if (!fs.existsSync(envLoaderPath)) {
    return false;
  }

  try {
    let content = fs.readFileSync(envLoaderPath, 'utf8');
    
    // Replace the env loading logic
    const oldPattern = /\/\/ Load \.env file if it exists[\s\S]*?require\('dotenv'\)\.config\(\{ path: envPath \}\);[\s\S]*?\}/;
    const newPattern = `// Load .env file from root directory
  const rootEnvPath = path.join(process.cwd(), '../../.env');
  const localEnvPath = path.join(process.cwd(), '.env');
  
  // Try root .env first, then local .env
  if (fs.existsSync(rootEnvPath)) {
    require('dotenv').config({ path: rootEnvPath });
  } else if (fs.existsSync(localEnvPath)) {
    require('dotenv').config({ path: localEnvPath });
  }`;

    if (content.match(oldPattern)) {
      content = content.replace(oldPattern, newPattern);
      fs.writeFileSync(envLoaderPath, content);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error fixing env-loader for ${path.basename(servicePath)}: ${error.message}`);
  }
  
  return false;
}

// Fix import paths in config files
function fixConfigImports(servicePath) {
  const configPath = path.join(servicePath, 'src', 'config');
  
  if (!fs.existsSync(configPath)) {
    return 0;
  }

  let fixedCount = 0;
  const configFiles = fs.readdirSync(configPath).filter(file => file.endsWith('.js'));
  
  for (const file of configFiles) {
    const filePath = path.join(configPath, file);
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;

      // Fix env-loader imports
      const patterns = [
        {
          old: /require\(['"`]\.\.\/\.\.\/utils\/env-loader['"`]\)/g,
          new: "require('../utils/env-loader')"
        },
        {
          old: /require\(['"`]\.\.\/\.\.\/\.\.\/shared\/utils\/env-loader['"`]\)/g,
          new: "require('../utils/env-loader')"
        }
      ];

      for (const { old, new: replacement } of patterns) {
        if (content.match(old)) {
          content = content.replace(old, replacement);
          modified = true;
        }
      }

      if (modified) {
        fs.writeFileSync(filePath, content);
        fixedCount++;
      }
    } catch (error) {
      console.error(`❌ Error fixing ${filePath}: ${error.message}`);
    }
  }
  
  return fixedCount;
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️ Service ${service} not found, skipping...`);
    continue;
  }

  console.log(`\n🔧 Processing ${service}...`);
  
  // Fix env-loader
  if (fixEnvLoader(servicePath)) {
    console.log(`  ✅ Fixed env-loader`);
  }
  
  // Fix config imports
  const configFixes = fixConfigImports(servicePath);
  if (configFixes > 0) {
    console.log(`  ✅ Fixed ${configFixes} config file(s)`);
  }
}

console.log('\n✅ Environment loading and import paths fixed!');
console.log('💡 Services will now load .env from the root directory');
