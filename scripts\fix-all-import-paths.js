#!/usr/bin/env node

/**
 * Script to fix all import paths systematically
 */

const fs = require('fs');
const path = require('path');

const services = [
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

console.log('🔧 Fixing all import paths systematically...');

function fixImportPaths(filePath, serviceName) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Determine the correct relative path based on file location
    const relativePath = path.relative(path.join('services', serviceName, 'src'), filePath);
    const depth = relativePath.split(path.sep).length - 1;
    
    let loggerPath, healthCheckPath, responsePath, envLoaderPath;
    
    if (depth === 0) {
      // File is in src/ directory
      loggerPath = './utils/logger';
      healthCheckPath = './utils/health-check';
      responsePath = './utils/response';
      envLoaderPath = './utils/env-loader';
    } else if (depth === 1) {
      // File is in src/subfolder/ directory
      loggerPath = '../utils/logger';
      healthCheckPath = '../utils/health-check';
      responsePath = '../utils/response';
      envLoaderPath = '../utils/env-loader';
    } else if (depth === 2) {
      // File is in src/subfolder/subfolder/ directory
      loggerPath = '../../utils/logger';
      healthCheckPath = '../../utils/health-check';
      responsePath = '../../utils/response';
      envLoaderPath = '../../utils/env-loader';
    } else {
      // Deeper nesting
      const prefix = '../'.repeat(depth);
      loggerPath = prefix + 'utils/logger';
      healthCheckPath = prefix + 'utils/health-check';
      responsePath = prefix + 'utils/response';
      envLoaderPath = prefix + 'utils/env-loader';
    }

    // Fix all possible import patterns
    const patterns = [
      // Logger imports
      {
        old: /require\(['"`]\.\/utils\/logger['"`]\)/g,
        new: `require('${loggerPath}')`
      },
      {
        old: /require\(['"`]\.\.\/utils\/logger['"`]\)/g,
        new: `require('${loggerPath}')`
      },
      {
        old: /require\(['"`]\.\.\/\.\.\/utils\/logger['"`]\)/g,
        new: `require('${loggerPath}')`
      },
      {
        old: /const \{ Logger \} = require\(['"`][^'"`]*utils\/logger['"`]\);/g,
        new: `const logger = require('${loggerPath}');`
      },
      // HealthCheck imports
      {
        old: /const \{ HealthCheck, commonChecks \} = require\(['"`][^'"`]*utils\/health-check['"`]\);/g,
        new: `const { HealthCheck, commonChecks } = require('${healthCheckPath}');`
      },
      {
        old: /const HealthCheck = require\(['"`][^'"`]*utils\/health-check['"`]\);/g,
        new: `const { HealthCheck } = require('${healthCheckPath}');`
      },
      // Response imports
      {
        old: /const ResponseFormatter = require\(['"`][^'"`]*utils\/response['"`]\);/g,
        new: `const ResponseFormatter = require('${responsePath}');`
      },
      // Env-loader imports
      {
        old: /const \{ setupEnvironment, getRequiredVarsForService \} = require\(['"`][^'"`]*utils\/env-loader['"`]\);/g,
        new: `const { setupEnvironment, getRequiredVarsForService } = require('${envLoaderPath}');`
      }
    ];

    for (const { old, new: replacement } of patterns) {
      if (content.match(old)) {
        content = content.replace(old, replacement);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}: ${error.message}`);
  }

  return false;
}

function processDirectory(dirPath, serviceName) {
  if (!fs.existsSync(dirPath)) {
    return 0;
  }

  let fixedCount = 0;
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
      fixedCount += processDirectory(itemPath, serviceName);
    } else if (item.endsWith('.js')) {
      if (fixImportPaths(itemPath, serviceName)) {
        console.log(`  ✅ Fixed ${path.relative(process.cwd(), itemPath)}`);
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    continue;
  }

  console.log(`\n🔧 Processing ${service}...`);
  
  const srcPath = path.join(servicePath, 'src');
  const fixedCount = processDirectory(srcPath, service);
  
  if (fixedCount > 0) {
    console.log(`  ✅ Fixed ${fixedCount} file(s)`);
  } else {
    console.log(`  ✅ Already clean`);
  }
}

console.log('\n✅ All import paths fixed!');
