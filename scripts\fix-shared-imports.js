#!/usr/bin/env node

/**
 * <PERSON>ript to fix remaining shared imports across all services
 * Replaces shared folder imports with local utility imports
 */

const fs = require('fs');
const path = require('path');

const services = [
  'api-gateway',
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

console.log('🔧 Fixing shared imports across all services...');

function fixImportsInFile(filePath, serviceName) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix shared utils imports
    const sharedUtilsPattern = /require\(['"`]\.\.\/\.\.\/\.\.\/shared\/utils\/env-loader['"`]\)/g;
    if (content.match(sharedUtilsPattern)) {
      content = content.replace(sharedUtilsPattern, "require('../utils/env-loader')");
      modified = true;
    }

    // Fix deeper shared utils imports (for config files)
    const deeperSharedPattern = /require\(['"`]\.\.\/\.\.\/\.\.\/\.\.\/shared\/utils\/env-loader['"`]\)/g;
    if (content.match(deeperSharedPattern)) {
      content = content.replace(deeperSharedPattern, "require('../../utils/env-loader')");
      modified = true;
    }

    // Fix @factcheck/shared imports
    const factcheckSharedPattern = /require\(['"`]@factcheck\/shared['"`]\)/g;
    if (content.match(factcheckSharedPattern)) {
      content = content.replace(factcheckSharedPattern, "require('../utils/logger')");
      modified = true;
    }

    // Fix specific shared imports
    const specificImports = [
      {
        pattern: /const \{ Logger \} = require\(['"`]@factcheck\/shared['"`]\);/g,
        replacement: "const logger = require('../utils/logger');"
      },
      {
        pattern: /const \{ HealthCheck, commonChecks \} = require\(['"`]@factcheck\/shared['"`]\);/g,
        replacement: "const { HealthCheck, commonChecks } = require('../utils/health-check');"
      },
      {
        pattern: /const \{ ResponseFormatter \} = require\(['"`]@factcheck\/shared['"`]\);/g,
        replacement: "const ResponseFormatter = require('../utils/response');"
      }
    ];

    for (const { pattern, replacement } of specificImports) {
      if (content.match(pattern)) {
        content = content.replace(pattern, replacement);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}: ${error.message}`);
  }

  return false;
}

function findAndFixFiles(servicePath, serviceName) {
  const srcPath = path.join(servicePath, 'src');
  if (!fs.existsSync(srcPath)) {
    return;
  }

  function processDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory()) {
        processDirectory(itemPath);
      } else if (item.endsWith('.js')) {
        if (fixImportsInFile(itemPath, serviceName)) {
          console.log(`  ✅ Fixed imports in ${path.relative(servicePath, itemPath)}`);
        }
      }
    }
  }

  processDirectory(srcPath);
}

function createEnvLoaderForService(servicePath, serviceName) {
  const utilsPath = path.join(servicePath, 'src', 'utils');
  const envLoaderPath = path.join(utilsPath, 'env-loader.js');
  
  if (fs.existsSync(envLoaderPath)) {
    return; // Already exists
  }

  if (!fs.existsSync(utilsPath)) {
    fs.mkdirSync(utilsPath, { recursive: true });
  }

  const envLoaderContent = `/**
 * Environment Loader for ${serviceName}
 * Simple environment variable validation and setup
 */

const fs = require('fs');
const path = require('path');

/**
 * Setup environment with validation
 */
function setupEnvironment(serviceName, requiredVars = [], strict = false) {
  // Load .env file if it exists
  const envPath = path.join(process.cwd(), '.env');
  if (fs.existsSync(envPath)) {
    require('dotenv').config({ path: envPath });
  }

  const missing = [];
  const warnings = [];

  // Check required variables
  for (const varName of requiredVars) {
    const value = process.env[varName];
    if (!value) {
      missing.push(varName);
    } else if (value.includes('your_') || value.includes('YOUR_') || value.includes('xxxxx')) {
      warnings.push(varName);
    }
  }

  if (missing.length > 0) {
    console.warn(\`⚠️ Missing required environment variables for \${serviceName}:\`, missing);
    if (strict) {
      throw new Error(\`Missing required environment variables: \${missing.join(', ')}\`);
    }
  }

  if (warnings.length > 0) {
    console.warn(\`⚠️ Environment variables with placeholder values for \${serviceName}:\`, warnings);
  }

  return {
    success: missing.length === 0,
    missing,
    warnings
  };
}

/**
 * Get required variables for specific service types
 */
function getRequiredVarsForService(serviceType) {
  switch (serviceType) {
    case 'api-gateway':
      return ['JWT_SECRET'];
    case 'auth':
      return ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY', 'JWT_SECRET'];
    case 'link':
      return ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY', 'JWT_SECRET'];
    case 'community':
      return ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY', 'JWT_SECRET'];
    case 'chat':
      return ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY', 'JWT_SECRET', 'GEMINI_API_KEY'];
    case 'news':
      return ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY', 'JWT_SECRET', 'NEWSAPI_API_KEY'];
    case 'admin':
      return ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY', 'JWT_SECRET'];
    default:
      return ['JWT_SECRET'];
  }
}

module.exports = {
  setupEnvironment,
  getRequiredVarsForService
};`;

  fs.writeFileSync(envLoaderPath, envLoaderContent);
  console.log(`  ✅ Created env-loader for ${serviceName}`);
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️ Service ${service} not found, skipping...`);
    continue;
  }

  console.log(`\n🔧 Processing ${service}...`);
  
  // Create env-loader if needed
  createEnvLoaderForService(servicePath, service);
  
  // Fix imports in all JS files
  findAndFixFiles(servicePath, service);
}

console.log('\n✅ All shared imports have been fixed!');
console.log('💡 Services now use local utilities instead of shared dependencies');
