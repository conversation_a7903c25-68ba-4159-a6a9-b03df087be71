#!/usr/bin/env node

/**
 * Complete Event Sourcing Demo Script
 * Demonstrates full event sourcing implementation across all microservices
 */

require('dotenv').config();

async function runCompleteEventSourcingDemo() {
  console.log('🚀 Starting Complete Event Sourcing Demo...');
  console.log('=' .repeat(80));
  
  try {
    // Initialize all event handlers
    console.log('\n📋 PHASE 1: Initialize All Event Handlers');
    console.log('-'.repeat(50));
    
    const AuthEventHandler = require('./services/auth-service/src/events/authEventHandler');
    const CommunityEventHandler = require('./services/community-service/src/events/communityEventHandler');
    const LinkEventHandler = require('./services/link-service/src/events/linkEventHandler');
    
    const authHandler = new AuthEventHandler();
    const communityHandler = new CommunityEventHandler();
    const linkHandler = new LinkEventHandler();
    
    // Wait for all connections
    const handlers = [
      { name: 'Auth Service', handler: authHandler },
      { name: 'Community Service', handler: communityHandler },
      { name: 'Link Service', handler: linkHandler }
    ];
    
    for (const { name, handler } of handlers) {
      await new Promise((resolve) => {
        if (handler.eventBus.isConnected) {
          console.log(`✅ ${name} EventBus already connected`);
          resolve();
          return;
        }
        
        handler.eventBus.on('connected', () => {
          console.log(`✅ ${name} EventBus connected`);
          resolve();
        });
        
        setTimeout(() => {
          console.log(`⚠️ ${name} EventBus connection timeout, continuing with mock`);
          resolve();
        }, 2000);
      });
    }
    
    console.log('\n📋 PHASE 2: User Authentication Flow');
    console.log('-'.repeat(50));
    
    // Simulate user registration
    console.log('👤 Simulating user registration...');
    await authHandler.publishUserCreatedEvent({
      id: 'demo-user-001',
      email: '<EMAIL>',
      name: 'Demo User',
      roles: ['user'],
      provider: 'email',
      emailVerified: true
    });
    
    // Simulate user login
    console.log('🔐 Simulating user login...');
    await authHandler.publishLoginEvent(
      {
        id: 'demo-user-001',
        email: '<EMAIL>',
        name: 'Demo User'
      },
      {
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Demo Browser)',
        location: 'Ho Chi Minh City, Vietnam'
      }
    );
    
    console.log('\n📋 PHASE 3: Link Security Analysis Flow');
    console.log('-'.repeat(50));
    
    // Simulate link scan request
    console.log('🔍 Simulating link scan request...');
    await linkHandler.publishLinkScanRequestedEvent({
      url: 'https://suspicious-demo-site.com',
      userId: 'demo-user-001',
      userEmail: '<EMAIL>',
      scanType: 'comprehensive',
      priority: 'high',
      requestId: 'demo-scan-001'
    });
    
    // Simulate security analysis
    console.log('🛡️ Simulating security analysis...');
    await linkHandler.publishSecurityAnalysisEvent({
      url: 'https://suspicious-demo-site.com',
      linkId: 'demo-link-001',
      analysisType: 'virus_total',
      results: {
        positives: 3,
        total: 67,
        scan_date: new Date().toISOString()
      },
      score: 45,
      recommendations: [
        'Potential threat detected',
        'Manual review recommended',
        'Proceed with caution'
      ]
    });
    
    // Simulate threat detection
    console.log('⚠️ Simulating threat detection...');
    await linkHandler.publishThreatDetectedEvent({
      url: 'https://suspicious-demo-site.com',
      linkId: 'demo-link-001',
      threatType: 'phishing',
      severity: 'high',
      description: 'Phishing site detected targeting social media credentials',
      source: 'phish-tank',
      confidence: 87,
      details: {
        targetSector: 'social-media',
        detectionMethod: 'url-pattern-analysis'
      }
    });
    
    // Simulate scan completion
    console.log('✅ Simulating scan completion...');
    await linkHandler.publishLinkScanCompletedEvent({
      url: 'https://suspicious-demo-site.com',
      linkId: 'demo-link-001',
      safetyScore: 45,
      riskLevel: 'high-risk',
      threats: ['phishing'],
      securityScores: {
        virusTotal: 45,
        phishTank: 30,
        criminalIP: 60
      },
      scanDuration: 3200,
      scanType: 'comprehensive'
    });
    
    console.log('\n📋 PHASE 4: Community Interaction Flow');
    console.log('-'.repeat(50));
    
    // Simulate post creation
    console.log('📝 Simulating post creation...');
    await communityHandler.publishPostCreatedEvent({
      id: 'demo-post-001',
      title: '⚠️ Cảnh báo: Trang web lừa đảo mới phát hiện',
      content: 'Tôi vừa phát hiện trang web https://suspicious-demo-site.com có dấu hiệu lừa đảo. Mọi người cẩn thận!',
      author: {
        uid: 'demo-user-001',
        email: '<EMAIL>',
        displayName: 'Demo User'
      },
      category: 'security-alert',
      tags: ['phishing', 'warning', 'security'],
      type: 'user_post'
    });
    
    // Simulate comment creation
    console.log('💬 Simulating comment creation...');
    await communityHandler.publishCommentCreatedEvent({
      id: 'demo-comment-001',
      linkId: 'demo-post-001',
      content: 'Cảm ơn bạn đã chia sẻ! Tôi cũng đã gặp trang web này và nó thực sự đáng nghi.',
      author: {
        uid: 'demo-user-002',
        email: '<EMAIL>',
        displayName: 'Community Member'
      },
      parentId: null
    });
    
    // Simulate voting
    console.log('👍 Simulating voting...');
    await communityHandler.publishVoteEvent({
      linkId: 'demo-post-001',
      userId: 'demo-user-002',
      voteType: 'upvote',
      action: 'created',
      previousVote: null
    });
    
    await communityHandler.publishVoteEvent({
      linkId: 'demo-post-001',
      userId: 'demo-user-003',
      voteType: 'upvote',
      action: 'created',
      previousVote: null
    });
    
    console.log('\n📋 PHASE 5: Administrative Actions');
    console.log('-'.repeat(50));
    
    // Simulate link verification
    console.log('🔒 Simulating link verification...');
    await linkHandler.publishLinkVerifiedEvent({
      url: 'https://suspicious-demo-site.com',
      linkId: 'demo-link-001',
      status: 'malicious',
      verifiedBy: 'admin-001',
      method: 'manual-review',
      confidence: 95,
      notes: 'Confirmed phishing site. Added to blacklist.'
    });
    
    // Simulate user logout
    console.log('👋 Simulating user logout...');
    await authHandler.publishLogoutEvent(
      {
        id: 'demo-user-001',
        email: '<EMAIL>',
        name: 'Demo User'
      },
      {
        sessionDuration: 1800000 // 30 minutes
      }
    );
    
    console.log('\n📋 PHASE 6: Event Sourcing Analytics');
    console.log('-'.repeat(50));
    
    // Collect analytics from all services
    const analytics = {
      auth: {
        status: authHandler.getStatus(),
        health: await authHandler.healthCheck(),
        mockEvents: authHandler.eventBus.getMockEvents().length
      },
      community: {
        status: communityHandler.getStatus(),
        health: await communityHandler.healthCheck(),
        mockEvents: communityHandler.eventBus.getMockEvents().length
      },
      link: {
        status: linkHandler.getStatus(),
        health: await linkHandler.healthCheck(),
        mockEvents: linkHandler.eventBus.getMockEvents().length
      }
    };
    
    console.log('\n📊 Event Sourcing Analytics Summary:');
    console.log('=' .repeat(80));
    
    let totalEvents = 0;
    for (const [service, data] of Object.entries(analytics)) {
      console.log(`\n🔧 ${service.toUpperCase()} SERVICE:`);
      console.log(`   Status: ${data.health.status}`);
      console.log(`   Mode: ${data.health.mode || 'unknown'}`);
      console.log(`   Events Published: ${data.mockEvents}`);
      console.log(`   Subscriptions: ${data.status.mockSubscriptionsCount || 0}`);
      totalEvents += data.mockEvents;
    }
    
    console.log(`\n🎯 TOTAL EVENTS PUBLISHED: ${totalEvents}`);
    
    // Show recent events from each service
    console.log('\n📝 Recent Events by Service:');
    console.log('=' .repeat(80));
    
    for (const [service, data] of Object.entries(analytics)) {
      const handler = service === 'auth' ? authHandler : 
                     service === 'community' ? communityHandler : linkHandler;
      const events = handler.eventBus.getMockEvents();
      
      if (events.length > 0) {
        console.log(`\n🔧 ${service.toUpperCase()} SERVICE EVENTS:`);
        events.slice(-3).forEach((event, index) => {
          console.log(`   ${index + 1}. ${event.type} (${event.timestamp})`);
          console.log(`      Source: ${event.source}`);
          console.log(`      Data: ${JSON.stringify(event.data, null, 8).substring(0, 100)}...`);
        });
      }
    }
    
    console.log('\n🎉 DEMO COMPLETED SUCCESSFULLY!');
    console.log('=' .repeat(80));
    console.log('✅ All microservices event sourcing working');
    console.log('✅ Cross-service event flow demonstrated');
    console.log('✅ Event persistence and retrieval working');
    console.log('✅ Event analytics and monitoring functional');
    console.log('✅ Fallback mechanisms operational');
    
    // Cleanup
    await authHandler.disconnect();
    await communityHandler.disconnect();
    await linkHandler.disconnect();
    
    console.log('\n👋 All services disconnected. Demo complete!');
    
  } catch (error) {
    console.error('❌ Complete Event Sourcing Demo failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

runCompleteEventSourcingDemo();
