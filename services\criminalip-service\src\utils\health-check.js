/**
 * Enhanced Health Check utilities for microservices
 * Provides comprehensive health monitoring and dependency checking
 */

const { EventEmitter } = require('events');

class HealthCheck extends EventEmitter {
  constructor(serviceName) {
    super();
    this.serviceName = serviceName || process.env.SERVICE_NAME || 'unknown-service';
    this.checks = new Map();
    this.dependencies = new Map();
    this.startTime = Date.now();
    this.version = process.env.SERVICE_VERSION || '1.0.0';
  }

  /**
   * Add a health check
   */
  addCheck(name, checkFunction, options = {}) {
    this.checks.set(name, {
      fn: checkFunction,
      timeout: options.timeout || 5000,
      critical: options.critical || false,
      description: options.description || `Health check for ${name}`
    });
  }

  /**
   * Add dependency check
   */
  addDependency(name, checkFunction, options = {}) {
    this.dependencies.set(name, {
      fn: checkFunction,
      timeout: options.timeout || 3000,
      critical: options.critical || true,
      description: options.description || `Dependency check for ${name}`
    });
  }

  /**
   * Remove a health check
   */
  removeCheck(name) {
    this.checks.delete(name);
  }

  /**
   * Run all health checks
   */
  async runChecks() {
    const results = {
      service: this.serviceName,
      version: this.version,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Date.now() - this.startTime,
      checks: {},
      dependencies: {}
    };

    let overallHealthy = true;

    // Run basic health checks
    for (const [name, check] of this.checks) {
      try {
        const startTime = Date.now();
        const result = await Promise.race([
          check.fn(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), check.timeout)
          )
        ]);
        
        const duration = Date.now() - startTime;
        
        results.checks[name] = {
          status: 'healthy',
          duration: `${duration}ms`,
          description: check.description,
          data: result || null
        };
      } catch (error) {
        results.checks[name] = {
          status: 'unhealthy',
          error: error.message,
          description: check.description,
          critical: check.critical
        };
        
        if (check.critical) {
          overallHealthy = false;
        }
      }
    }

    // Run dependency checks
    for (const [name, dependency] of this.dependencies) {
      try {
        const startTime = Date.now();
        const result = await Promise.race([
          dependency.fn(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Dependency check timeout')), dependency.timeout)
          )
        ]);
        
        const duration = Date.now() - startTime;
        
        results.dependencies[name] = {
          status: 'healthy',
          duration: `${duration}ms`,
          description: dependency.description,
          data: result || null
        };
      } catch (error) {
        results.dependencies[name] = {
          status: 'unhealthy',
          error: error.message,
          description: dependency.description,
          critical: dependency.critical
        };
        
        if (dependency.critical) {
          overallHealthy = false;
        }
      }
    }

    // Set overall status
    results.status = overallHealthy ? 'healthy' : 'unhealthy';
    
    // Add system information
    results.system = {
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      platform: process.platform,
      nodeVersion: process.version,
      pid: process.pid
    };

    return results;
  }

  /**
   * Express middleware for health check endpoint
   */
  middleware() {
    return async (req, res) => {
      try {
        const results = await this.runChecks();
        const statusCode = results.status === 'healthy' ? 200 : 503;
        
        res.status(statusCode).json(results);
      } catch (error) {
        res.status(500).json({
          service: this.serviceName,
          status: 'error',
          timestamp: new Date().toISOString(),
          error: error.message
        });
      }
    };
  }

  /**
   * Simple liveness probe (always returns OK if service is running)
   */
  liveness() {
    return (req, res) => {
      res.status(200).json({
        service: this.serviceName,
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime
      });
    };
  }

  /**
   * Readiness probe (checks if service is ready to handle requests)
   */
  readiness() {
    return async (req, res) => {
      try {
        const results = await this.runChecks();
        const statusCode = results.status === 'healthy' ? 200 : 503;
        
        res.status(statusCode).json({
          service: this.serviceName,
          status: results.status === 'healthy' ? 'ready' : 'not ready',
          timestamp: new Date().toISOString(),
          checks: results.checks
        });
      } catch (error) {
        res.status(503).json({
          service: this.serviceName,
          status: 'not ready',
          timestamp: new Date().toISOString(),
          error: error.message
        });
      }
    };
  }
}

/**
 * Common health check functions
 */
const commonChecks = {
  /**
   * Database connection check
   */
  database: (db) => {
    return async () => {
      // Skip database check in test environment
      if (process.env.NODE_ENV === 'test') {
        return 'Database connection OK (test environment)';
      }

      if (!db) {
        throw new Error('Database not initialized');
      }

      // For Firestore
      if (db.collection) {
        await db.collection('health_check').limit(1).get();
        return 'Database connection OK';
      }

      // For other databases, implement specific checks
      throw new Error('Unknown database type');
    };
  },

  /**
   * Redis connection check
   */
  redis: (redisClient) => {
    return async () => {
      // Skip Redis check in test environment
      if (process.env.NODE_ENV === 'test') {
        return 'Redis connection OK (test environment)';
      }

      if (!redisClient) {
        throw new Error('Redis client not initialized');
      }

      await redisClient.ping();
      return 'Redis connection OK';
    };
  },

  /**
   * External service check
   */
  externalService: (serviceName, url) => {
    return async () => {
      // Skip external service check in test environment
      if (process.env.NODE_ENV === 'test') {
        return `${serviceName} is reachable (test environment)`;
      }

      const axios = require('axios');

      try {
        const response = await axios.get(url, { timeout: 3000 });
        return `${serviceName} is reachable (${response.status})`;
      } catch (error) {
        throw new Error(`${serviceName} is unreachable: ${error.message}`);
      }
    };
  },

  /**
   * Memory usage check
   */
  memory: (maxMemoryMB = 512) => {
    return async () => {
      const memUsage = process.memoryUsage();
      const memUsageMB = memUsage.heapUsed / 1024 / 1024;
      
      if (memUsageMB > maxMemoryMB) {
        throw new Error(`Memory usage too high: ${memUsageMB.toFixed(2)}MB > ${maxMemoryMB}MB`);
      }
      
      return `Memory usage OK: ${memUsageMB.toFixed(2)}MB`;
    };
  },

  /**
   * Disk space check
   */
  diskSpace: (path = '/', minFreeGB = 1) => {
    return async () => {
      const fs = require('fs').promises;
      
      try {
        const stats = await fs.statfs(path);
        const freeGB = (stats.bavail * stats.bsize) / (1024 * 1024 * 1024);
        
        if (freeGB < minFreeGB) {
          throw new Error(`Low disk space: ${freeGB.toFixed(2)}GB < ${minFreeGB}GB`);
        }
        
        return `Disk space OK: ${freeGB.toFixed(2)}GB free`;
      } catch (error) {
        throw new Error(`Disk space check failed: ${error.message}`);
      }
    };
  }
};

module.exports = {
  HealthCheck,
  commonChecks
};
