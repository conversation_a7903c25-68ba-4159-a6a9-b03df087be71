#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to remove @factcheck/shared dependencies from all services
 * This completes the microservices isolation process
 */

const fs = require('fs');
const path = require('path');

const services = [
  'api-gateway',
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

console.log('🔧 Removing @factcheck/shared dependencies from services...');

for (const service of services) {
  const packageJsonPath = path.join('services', service, 'package.json');
  
  // Check if package.json exists
  if (!fs.existsSync(packageJsonPath)) {
    console.log(`⚠️ Package.json not found for ${service}, skipping...`);
    continue;
  }

  try {
    // Read package.json
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Remove @factcheck/shared dependency
    if (packageJson.dependencies && packageJson.dependencies['@factcheck/shared']) {
      delete packageJson.dependencies['@factcheck/shared'];
      console.log(`✅ Removed @factcheck/shared from ${service}`);
    }

    // Write back package.json
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    
  } catch (error) {
    console.error(`❌ Error processing ${service}: ${error.message}`);
  }
}

console.log('✅ Shared dependencies removed from all services!');
console.log('💡 Next: Run "npm run install:all" to update dependencies');
