#!/usr/bin/env node

/**
 * Script to fix event syntax errors in event handlers
 */

const fs = require('fs');
const path = require('path');

const services = [
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service'
];

console.log('🔧 Fixing event syntax errors...');

function fixEventSyntax(filePath) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix all event syntax errors
    const patterns = [
      {
        old: /case ''/g,
        new: "case '"
      },
      {
        old: /case ''([A-Z_\.]+):/g,
        new: "case '$1':"
      }
    ];

    for (const { old, new: replacement } of patterns) {
      if (content.match(old)) {
        content = content.replace(old, replacement);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}: ${error.message}`);
  }

  return false;
}

function processService(servicePath, serviceName) {
  const eventsPath = path.join(servicePath, 'src', 'events');
  
  if (!fs.existsSync(eventsPath)) {
    return 0;
  }

  let fixedCount = 0;
  const files = fs.readdirSync(eventsPath);
  
  for (const file of files) {
    if (file.endsWith('.js')) {
      const filePath = path.join(eventsPath, file);
      if (fixEventSyntax(filePath)) {
        console.log(`  ✅ Fixed ${serviceName}/${file}`);
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    continue;
  }

  console.log(`\n🔧 Processing ${service}...`);
  
  const fixedCount = processService(servicePath, service);
  
  if (fixedCount > 0) {
    console.log(`  ✅ Fixed ${fixedCount} file(s)`);
  } else {
    console.log(`  ✅ No event files or already clean`);
  }
}

console.log('\n✅ Event syntax errors fixed!');
