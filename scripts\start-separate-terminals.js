#!/usr/bin/env node

/**
 * Start each service in a separate terminal window
 * Supports Windows (cmd), macOS (Terminal), and Linux (gnome-terminal/xterm)
 */

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');
const fs = require('fs');

class SeparateTerminalStarter {
  constructor() {
    this.platform = os.platform();
    this.projectRoot = process.cwd();
    
    // Services to start in order
    this.services = [
      { name: 'auth-service', port: 3001, title: 'Auth Service' },
      { name: 'link-service', port: 3002, title: 'Link Service' },
      { name: 'community-service', port: 3003, title: 'Community Service' },
      { name: 'chat-service', port: 3004, title: 'Chat Service' },
      { name: 'news-service', port: 3005, title: 'News Service' },
      { name: 'admin-service', port: 3006, title: 'Admin Service' },
      { name: 'api-gateway', port: 8080, title: 'API Gateway' }
    ];
  }

  async start() {
    console.log('🚀 Starting FactCheck Platform with separate terminals...');
    console.log('='.repeat(60));

    try {
      // Validate environment first
      await this.validateEnvironment();
      
      // Start each service in its own terminal
      for (const service of this.services) {
        await this.startServiceInTerminal(service);
        // Small delay between starts
        await this.delay(1000);
      }

      console.log('\n✅ All services started in separate terminals!');
      this.showInfo();

    } catch (error) {
      console.error('❌ Failed to start services:', error.message);
      process.exit(1);
    }
  }

  async validateEnvironment() {
    const envPath = path.join(this.projectRoot, '.env');
    if (!fs.existsSync(envPath)) {
      throw new Error('.env file not found. Run "npm run env:setup" first.');
    }
    console.log('✅ Environment validated');
  }

  async startServiceInTerminal(service) {
    const servicePath = path.join(this.projectRoot, 'services', service.name);
    
    if (!fs.existsSync(servicePath)) {
      console.log(`⚠️ Service directory not found: ${service.name}`);
      return;
    }

    console.log(`🚀 Starting ${service.title} in new terminal...`);

    try {
      if (this.platform === 'win32') {
        // Windows - use start command to open new cmd window
        await this.startWindowsTerminal(service, servicePath);
      } else if (this.platform === 'darwin') {
        // macOS - use Terminal.app
        await this.startMacTerminal(service, servicePath);
      } else {
        // Linux - try gnome-terminal, then xterm
        await this.startLinuxTerminal(service, servicePath);
      }
      
      console.log(`  ✅ ${service.title} terminal opened`);
    } catch (error) {
      console.log(`  ❌ Failed to start ${service.title}: ${error.message}`);
    }
  }

  async startWindowsTerminal(service, servicePath) {
    // Windows: start new cmd window with title
    const command = `start "${service.title} - Port ${service.port}" cmd /k "cd /d ${servicePath} && npm start"`;
    
    return new Promise((resolve, reject) => {
      const child = spawn('cmd', ['/c', command], {
        detached: true,
        stdio: 'ignore'
      });
      
      child.unref();
      
      // Give it a moment to start
      setTimeout(() => resolve(), 500);
      
      child.on('error', reject);
    });
  }

  async startMacTerminal(service, servicePath) {
    // macOS: use osascript to open Terminal.app
    const script = `
      tell application "Terminal"
        activate
        do script "cd '${servicePath}' && echo 'Starting ${service.title} on port ${service.port}...' && npm start"
        set custom title of front window to "${service.title} - Port ${service.port}"
      end tell
    `;
    
    return new Promise((resolve, reject) => {
      const child = spawn('osascript', ['-e', script], {
        detached: true,
        stdio: 'ignore'
      });
      
      child.unref();
      setTimeout(() => resolve(), 500);
      child.on('error', reject);
    });
  }

  async startLinuxTerminal(service, servicePath) {
    // Linux: try gnome-terminal first, then xterm
    const commands = [
      // GNOME Terminal
      {
        cmd: 'gnome-terminal',
        args: [
          '--title', `${service.title} - Port ${service.port}`,
          '--working-directory', servicePath,
          '--', 'bash', '-c', `echo "Starting ${service.title} on port ${service.port}..." && npm start; exec bash`
        ]
      },
      // XTerm fallback
      {
        cmd: 'xterm',
        args: [
          '-title', `${service.title} - Port ${service.port}`,
          '-e', `bash -c "cd '${servicePath}' && echo 'Starting ${service.title} on port ${service.port}...' && npm start; exec bash"`
        ]
      },
      // Konsole (KDE)
      {
        cmd: 'konsole',
        args: [
          '--title', `${service.title} - Port ${service.port}`,
          '--workdir', servicePath,
          '-e', 'bash', '-c', `echo "Starting ${service.title} on port ${service.port}..." && npm start; exec bash`
        ]
      }
    ];

    for (const { cmd, args } of commands) {
      try {
        return await new Promise((resolve, reject) => {
          const child = spawn(cmd, args, {
            detached: true,
            stdio: 'ignore'
          });
          
          child.unref();
          setTimeout(() => resolve(), 500);
          child.on('error', reject);
        });
      } catch (error) {
        // Try next terminal
        continue;
      }
    }
    
    throw new Error('No suitable terminal emulator found. Please install gnome-terminal, xterm, or konsole.');
  }

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  showInfo() {
    console.log('\n🌐 Service URLs:');
    console.log('='.repeat(40));
    this.services.forEach(service => {
      console.log(`${service.title.padEnd(20)} http://localhost:${service.port}`);
    });
    
    console.log('\n📋 Commands:');
    console.log('Check status: npm run status');
    console.log('Stop all:     npm stop');
    console.log('Health check: npm run health');
    
    console.log('\n💡 Tips:');
    console.log('- Each service runs in its own terminal window');
    console.log('- Close terminal windows to stop individual services');
    console.log('- Use "npm stop" to stop all services at once');
    console.log('- Wait 1-2 minutes for all services to be ready');
    
    console.log('\n🎯 Next steps:');
    console.log('1. Wait for all services to start (check terminal windows)');
    console.log('2. Start the client: cd client && npm start');
    console.log('3. Open http://localhost:3000 in your browser');
  }
}

// Run if called directly
if (require.main === module) {
  const starter = new SeparateTerminalStarter();
  starter.start().catch(console.error);
}

module.exports = SeparateTerminalStarter;
