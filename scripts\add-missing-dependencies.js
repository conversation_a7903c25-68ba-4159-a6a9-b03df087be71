#!/usr/bin/env node

/**
 * Script to add missing dependencies to services
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const services = [
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

// Common dependencies that all services need
const commonDeps = [
  'dotenv',
  'winston'
];

// Service-specific dependencies
const serviceDeps = {
  'auth-service': ['node-cron', 'rss-parser'],
  'link-service': [],
  'community-service': [],
  'chat-service': [],
  'news-service': [],
  'admin-service': [],
  'phishtank-service': [],
  'criminalip-service': []
};

console.log('🔧 Adding missing dependencies to services...');

function addDependencies(servicePath, serviceName, deps) {
  const packageJsonPath = path.join(servicePath, 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log(`  ⚠️ package.json not found for ${serviceName}`);
    return;
  }

  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const currentDeps = packageJson.dependencies || {};
    
    const missingDeps = deps.filter(dep => !currentDeps[dep]);
    
    if (missingDeps.length > 0) {
      console.log(`  📦 Installing missing dependencies for ${serviceName}: ${missingDeps.join(', ')}`);
      
      // Change to service directory and install
      process.chdir(servicePath);
      
      for (const dep of missingDeps) {
        try {
          execSync(`npm install ${dep}`, { stdio: 'inherit' });
        } catch (error) {
          console.error(`  ❌ Failed to install ${dep}: ${error.message}`);
        }
      }
      
      // Change back to root
      process.chdir('../../..');
    } else {
      console.log(`  ✅ All dependencies present for ${serviceName}`);
    }
  } catch (error) {
    console.error(`  ❌ Error processing ${serviceName}: ${error.message}`);
  }
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️ Service ${service} not found, skipping...`);
    continue;
  }

  console.log(`\n🔧 Processing ${service}...`);
  
  // Combine common and service-specific dependencies
  const allDeps = [...commonDeps, ...(serviceDeps[service] || [])];
  
  addDependencies(servicePath, service, allDeps);
}

console.log('\n✅ Dependencies check complete!');
