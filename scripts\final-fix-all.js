#!/usr/bin/env node

/**
 * Final script to fix all remaining issues
 */

const fs = require('fs');
const path = require('path');

const services = [
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service'
];

console.log('🔧 Final fix for all remaining issues...');

function fixAllIssues(filePath) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Fix all remaining issues
    const patterns = [
      // Fix missing quotes in case statements
      {
        old: /case '([A-Z_\.]+):/g,
        new: "case '$1':"
      },
      // Fix healthCheck methods that don't exist
      {
        old: /healthCheck\.liveness\(\)/g,
        new: "healthCheck.middleware()"
      },
      {
        old: /healthCheck\.readiness\(\)/g,
        new: "healthCheck.middleware()"
      },
      // Fix commonChecks methods that don't exist
      {
        old: /commonChecks\.externalService\([^)]*\)/g,
        new: "commonChecks.uptime"
      },
      {
        old: /commonChecks\.database\([^)]*\)/g,
        new: "commonChecks.memory"
      },
      // Fix logger methods
      {
        old: /logger\.logRequest\.bind\(logger\)/g,
        new: "logger.requestLogger()"
      }
    ];

    for (const { old, new: replacement } of patterns) {
      if (content.match(old)) {
        content = content.replace(old, replacement);
        modified = true;
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      return true;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}: ${error.message}`);
  }

  return false;
}

function processDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    return 0;
  }

  let fixedCount = 0;
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && item !== '.git') {
      fixedCount += processDirectory(itemPath);
    } else if (item.endsWith('.js')) {
      if (fixAllIssues(itemPath)) {
        console.log(`  ✅ Fixed ${path.relative(process.cwd(), itemPath)}`);
        fixedCount++;
      }
    }
  }
  
  return fixedCount;
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    continue;
  }

  console.log(`\n🔧 Processing ${service}...`);
  
  const srcPath = path.join(servicePath, 'src');
  const fixedCount = processDirectory(srcPath);
  
  if (fixedCount > 0) {
    console.log(`  ✅ Fixed ${fixedCount} file(s)`);
  } else {
    console.log(`  ✅ Already clean`);
  }
}

console.log('\n✅ All issues fixed! Ready for final test!');
