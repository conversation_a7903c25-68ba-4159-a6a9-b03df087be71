#!/usr/bin/env node

/**
 * <PERSON>ript to fix env-loader import paths in app.js files
 * Changes '../utils/env-loader' to './utils/env-loader' for files in src directory
 */

const fs = require('fs');
const path = require('path');

const services = [
  'api-gateway',
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

console.log('🔧 Fixing env-loader import paths...');

for (const service of services) {
  const appJsPath = path.join('services', service, 'src', 'app.js');
  
  if (!fs.existsSync(appJsPath)) {
    console.log(`⚠️ app.js not found for ${service}, skipping...`);
    continue;
  }

  try {
    let content = fs.readFileSync(appJsPath, 'utf8');
    let modified = false;

    // Fix the import path
    const oldPattern = /require\(['"`]\.\.\/utils\/env-loader['"`]\)/g;
    if (content.match(oldPattern)) {
      content = content.replace(oldPattern, "require('./utils/env-loader')");
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(appJsPath, content);
      console.log(`✅ Fixed env-loader path in ${service}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${service}: ${error.message}`);
  }
}

console.log('✅ All env-loader paths fixed!');
