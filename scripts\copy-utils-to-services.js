#!/usr/bin/env node

/**
 * <PERSON>ript to copy shared utilities to individual services
 * This removes the dependency on shared folders for proper microservices isolation
 */

const fs = require('fs');
const path = require('path');

const services = [
  'api-gateway',
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

// Utility files to copy
const utilities = {
  'logger.js': `/**
 * Logger utility for {SERVICE_NAME}
 * Standardized logging across the microservice
 */

const winston = require('winston');

class Logger {
  constructor(serviceName = '{SERVICE_NAME}') {
    this.serviceName = serviceName;
    
    // Create winston logger instance
    this.logger = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json(),
        winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
          return JSON.stringify({
            timestamp,
            level,
            service: service || this.serviceName,
            message,
            ...meta
          });
        })
      ),
      defaultMeta: { service: this.serviceName },
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        })
      ]
    });

    // Add file transport in production
    if (process.env.NODE_ENV === 'production') {
      this.logger.add(new winston.transports.File({
        filename: \`logs/\${this.serviceName}-error.log\`,
        level: 'error'
      }));
      this.logger.add(new winston.transports.File({
        filename: \`logs/\${this.serviceName}.log\`
      }));
    }
  }

  info(message, meta = {}) {
    this.logger.info(message, meta);
  }

  error(message, meta = {}) {
    this.logger.error(message, meta);
  }

  warn(message, meta = {}) {
    this.logger.warn(message, meta);
  }

  debug(message, meta = {}) {
    this.logger.debug(message, meta);
  }

  // Express middleware for request logging
  requestLogger() {
    return (req, res, next) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        this.info('HTTP Request', {
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          duration: \`\${duration}ms\`,
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });
      });
      
      next();
    };
  }
}

// Create logger instance for {SERVICE_NAME}
const logger = new Logger('{SERVICE_NAME}');

module.exports = logger;`,

  'health-check.js': `/**
 * Health Check utility for {SERVICE_NAME}
 * Provides health monitoring and status checks
 */

const logger = require('./logger');

class HealthCheck {
  constructor(serviceName = '{SERVICE_NAME}') {
    this.serviceName = serviceName;
    this.checks = new Map();
    this.status = 'healthy';
    this.lastCheck = null;
  }

  // Add a health check
  addCheck(name, checkFunction, options = {}) {
    this.checks.set(name, {
      name,
      check: checkFunction,
      timeout: options.timeout || 5000,
      critical: options.critical !== false,
      lastResult: null,
      lastRun: null
    });
  }

  // Run all health checks
  async runChecks() {
    const results = {};
    let overallStatus = 'healthy';

    for (const [name, checkConfig] of this.checks) {
      try {
        const startTime = Date.now();
        const result = await Promise.race([
          checkConfig.check(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), checkConfig.timeout)
          )
        ]);

        const duration = Date.now() - startTime;
        
        results[name] = {
          status: 'healthy',
          duration: \`\${duration}ms\`,
          result,
          timestamp: new Date().toISOString()
        };

        checkConfig.lastResult = results[name];
        checkConfig.lastRun = new Date();

      } catch (error) {
        results[name] = {
          status: 'unhealthy',
          error: error.message,
          timestamp: new Date().toISOString()
        };

        checkConfig.lastResult = results[name];
        checkConfig.lastRun = new Date();

        if (checkConfig.critical) {
          overallStatus = 'unhealthy';
        }

        logger.error(\`Health check failed: \${name}\`, { error: error.message });
      }
    }

    this.status = overallStatus;
    this.lastCheck = new Date();

    return {
      service: this.serviceName,
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results
    };
  }

  // Express middleware for health endpoint
  middleware() {
    return async (req, res) => {
      try {
        const healthStatus = await this.runChecks();
        const statusCode = healthStatus.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(healthStatus);
      } catch (error) {
        logger.error('Health check middleware error', { error: error.message });
        res.status(500).json({
          service: this.serviceName,
          status: 'error',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    };
  }
}

// Common health checks
const commonChecks = {
  // Memory usage check
  memory: () => {
    const usage = process.memoryUsage();
    const totalMB = Math.round(usage.rss / 1024 / 1024);
    
    if (totalMB > 512) { // Alert if using more than 512MB
      throw new Error(\`High memory usage: \${totalMB}MB\`);
    }
    
    return { memoryUsage: \`\${totalMB}MB\` };
  },

  // Uptime check
  uptime: () => {
    const uptimeSeconds = process.uptime();
    return { uptime: \`\${Math.floor(uptimeSeconds)}s\` };
  },

  // Environment check
  environment: () => {
    return {
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development'
    };
  }
};

module.exports = {
  HealthCheck,
  commonChecks
};`,

  'response.js': `/**
 * Standardized API Response Utility for {SERVICE_NAME}
 * Ensures consistent response format across the microservice
 */

const logger = require('./logger');

class ResponseFormatter {
  constructor(serviceName = '{SERVICE_NAME}') {
    this.serviceName = serviceName;
  }

  // Success response
  success(res, data = null, message = 'Success', statusCode = 200) {
    const response = {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
      service: this.serviceName
    };

    logger.info('API Success Response', {
      statusCode,
      message,
      hasData: !!data
    });

    return res.status(statusCode).json(response);
  }

  // Error response
  error(res, message = 'Internal Server Error', statusCode = 500, details = null) {
    const response = {
      success: false,
      message,
      error: details,
      timestamp: new Date().toISOString(),
      service: this.serviceName
    };

    logger.error('API Error Response', {
      statusCode,
      message,
      details
    });

    return res.status(statusCode).json(response);
  }

  // Validation error response
  validationError(res, errors, message = 'Validation failed') {
    return this.error(res, message, 400, { validationErrors: errors });
  }

  // Not found response
  notFound(res, resource = 'Resource', message = null) {
    const defaultMessage = \`\${resource} not found\`;
    return this.error(res, message || defaultMessage, 404);
  }

  // Unauthorized response
  unauthorized(res, message = 'Unauthorized access') {
    return this.error(res, message, 401);
  }

  // Forbidden response
  forbidden(res, message = 'Access forbidden') {
    return this.error(res, message, 403);
  }

  // Conflict response
  conflict(res, message = 'Resource conflict', details = null) {
    return this.error(res, message, 409, details);
  }

  // Too many requests response
  tooManyRequests(res, message = 'Too many requests') {
    return this.error(res, message, 429);
  }

  // Express middleware for error handling
  errorHandler() {
    return (error, req, res, next) => {
      // Log the error
      logger.error('Unhandled API Error', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method
      });

      // Handle specific error types
      if (error.name === 'ValidationError') {
        return this.validationError(res, error.details);
      }

      if (error.name === 'UnauthorizedError') {
        return this.unauthorized(res, error.message);
      }

      if (error.name === 'CastError') {
        return this.error(res, 'Invalid ID format', 400);
      }

      // Default error response
      return this.error(res, 'Internal Server Error', 500, 
        process.env.NODE_ENV === 'development' ? error.stack : null
      );
    };
  }
}

module.exports = ResponseFormatter;`
};

console.log('🔧 Copying utilities to individual services...');

for (const service of services) {
  const servicePath = path.join('services', service);
  
  // Check if service exists
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️ Service ${service} not found, skipping...`);
    continue;
  }

  // Create utils directory
  const utilsPath = path.join(servicePath, 'src', 'utils');
  if (!fs.existsSync(utilsPath)) {
    fs.mkdirSync(utilsPath, { recursive: true });
  }

  // Copy each utility file
  for (const [filename, content] of Object.entries(utilities)) {
    const filePath = path.join(utilsPath, filename);
    const serviceContent = content.replace(/{SERVICE_NAME}/g, service);
    
    fs.writeFileSync(filePath, serviceContent);
    console.log(`✅ Created ${service}/src/utils/${filename}`);
  }
}

console.log('✅ Utilities copied to all services!');
