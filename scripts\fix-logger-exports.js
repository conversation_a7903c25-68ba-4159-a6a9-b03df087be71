#!/usr/bin/env node

/**
 * <PERSON>ript to fix logger exports in all services
 */

const fs = require('fs');
const path = require('path');

const services = [
  'auth-service', 
  'link-service',
  'community-service',
  'chat-service',
  'news-service',
  'admin-service',
  'phishtank-service',
  'criminalip-service'
];

console.log('🔧 Fixing logger exports in all services...');

function fixLoggerFile(servicePath, serviceName) {
  const loggerPath = path.join(servicePath, 'src', 'utils', 'logger.js');
  
  if (!fs.existsSync(loggerPath)) {
    console.log(`  ⚠️ Logger file not found for ${serviceName}`);
    return false;
  }

  try {
    let content = fs.readFileSync(loggerPath, 'utf8');
    let modified = false;

    // Fix the logger export issue
    const patterns = [
      {
        old: /\/\/ Create logger instance for [^\\n]*\\n\/\/ Logger already initialized\\n\\nmodule\.exports = logger;/g,
        new: `// Create logger instance for ${serviceName}\nconst logger = new Logger('${serviceName}');\n\nmodule.exports = logger;`
      },
      {
        old: /\/\/ Logger already initialized\\n\\nmodule\.exports = logger;/g,
        new: `const logger = new Logger('${serviceName}');\n\nmodule.exports = logger;`
      },
      {
        old: /module\.exports = logger;$/,
        new: function(match, offset, string) {
          // Check if logger is defined before this line
          const beforeMatch = string.substring(0, offset);
          if (!beforeMatch.includes('const logger = new Logger')) {
            return `const logger = new Logger('${serviceName}');\n\nmodule.exports = logger;`;
          }
          return match;
        }
      }
    ];

    for (const { old, new: replacement } of patterns) {
      if (typeof old === 'object' && old.test && old.test(content)) {
        content = content.replace(old, replacement);
        modified = true;
        break;
      } else if (typeof old === 'string' && content.includes(old)) {
        content = content.replace(old, replacement);
        modified = true;
        break;
      }
    }

    // If still not fixed, check if we need to add logger instance
    if (!modified && content.includes('module.exports = logger') && !content.includes('const logger = new Logger')) {
      content = content.replace(
        'module.exports = logger;',
        `const logger = new Logger('${serviceName}');\n\nmodule.exports = logger;`
      );
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(loggerPath, content);
      return true;
    }
  } catch (error) {
    console.error(`  ❌ Error fixing logger for ${serviceName}: ${error.message}`);
  }

  return false;
}

// Process each service
for (const service of services) {
  const servicePath = path.join('services', service);
  
  if (!fs.existsSync(servicePath)) {
    console.log(`⚠️ Service ${service} not found, skipping...`);
    continue;
  }

  console.log(`\n🔧 Fixing logger for ${service}...`);
  
  if (fixLoggerFile(servicePath, service)) {
    console.log(`  ✅ Fixed logger export`);
  } else {
    console.log(`  ✅ Logger already correct`);
  }
}

console.log('\n✅ Logger exports fixed!');
